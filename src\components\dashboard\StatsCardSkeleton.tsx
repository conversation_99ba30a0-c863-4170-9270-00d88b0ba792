'use client';

import { Card, CardContent, CardHeader } from '@/components/ui/card';

const StatsCardSkeleton = () => {
  return (
    <Card className="border border-gray-200 shadow-sm rounded-xl h-full backdrop-blur-sm">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4 pt-6 px-6">
        <div className="flex flex-col space-y-2">
          <div className="h-4 bg-gray-200 rounded animate-pulse w-24"></div>
          <div className="h-3 bg-gray-100 rounded animate-pulse w-16"></div>
        </div>
        <div className="rounded-xl p-3 bg-gray-200 animate-pulse w-12 h-12"></div>
      </CardHeader>
      <CardContent className="px-6 pb-6">
        <div className="h-10 bg-gray-200 rounded animate-pulse mb-3 w-20"></div>
        <div className="space-y-3">
          <div className="h-4 bg-gray-100 rounded animate-pulse w-32"></div>
          <div className="flex items-center gap-2">
            <div className="h-6 bg-gray-100 rounded-full animate-pulse w-16"></div>
            <div className="h-3 bg-gray-100 rounded animate-pulse w-24"></div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default StatsCardSkeleton;
