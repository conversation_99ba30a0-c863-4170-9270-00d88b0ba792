/* Modern Chat Interface Styles with Application Color Scheme */
/* Colors: #133157 (primary), #febd49 (accent), #e0e0e0 (secondary), white (background) */

/* CSS Custom Properties for Consistent Spacing and Colors */
:root {
  /* Spacing System */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;

  /* Color System for Status Indicators */
  --status-pending: #F59E0B;
  --status-in-progress: #3B82F6;
  --status-resolved: #10B981;

  /* Color System for Priority Indicators */
  --priority-urgent: #EF4444;
  --priority-high: #EF4444;
  --priority-medium: #F59E0B;
  --priority-low: #10B981;

  /* Application Colors */
  --primary-color: #133157;
  --accent-color: #febd49;
  --secondary-color: #e0e0e0;
  --background-color: #ffffff;

  /* Card System */
  --card-border-radius: 6px;
  --card-shadow: 0 2px 8px rgba(19, 49, 87, 0.08);
  --card-shadow-hover: 0 4px 12px rgba(19, 49, 87, 0.12);

  /* Typography */
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;

  /* Transitions */
  --transition-fast: all 0.2s ease-in-out;
  --transition-medium: all 0.3s ease-in-out;
}

/* Chat Card Specific Styles */
.chat-card {
  border-radius: var(--card-border-radius);
  box-shadow: var(--card-shadow);
  transition: var(--transition-fast);
  background: var(--background-color);
  border: 1px solid #e5e7eb;
  min-height: 190px;
}

.chat-card:hover {
  box-shadow: var(--card-shadow-hover);
  transform: translateY(-2px);
}

.chat-card-header {
  padding: var(--spacing-md) var(--spacing-md) var(--spacing-sm);
  border-bottom: 1px solid #f1f5f9;
}

.chat-card-content {
  padding: var(--spacing-sm) var(--spacing-md);
}

.chat-card-footer {
  padding: var(--spacing-sm) var(--spacing-md) var(--spacing-md);
  border-top: 1px solid #f1f5f9;
  background: linear-gradient(to right, #f8f9fa, #ffffff);
}

/* Status Badge System */
.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 3px 6px;
  border-radius: 3px;
  font-size: 0.65rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.025em;
  border: none;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.status-badge-pending {
  background: var(--status-pending);
  color: white;
}

.status-badge-in-progress {
  background: var(--status-in-progress);
  color: white;
}

.status-badge-resolved {
  background: var(--status-resolved);
  color: white;
}

/* Priority Indicator System */
.priority-indicator {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 3px 6px;
  border-radius: 3px;
  font-size: 0.65rem;
  font-weight: 600;
}

.priority-urgent {
  color: var(--priority-urgent);
  background: rgba(239, 68, 68, 0.1);
}

.priority-high {
  color: var(--priority-high);
  background: rgba(239, 68, 68, 0.1);
}

.priority-medium {
  color: var(--priority-medium);
  background: rgba(245, 158, 11, 0.1);
}

.priority-low {
  color: var(--priority-low);
  background: rgba(16, 185, 129, 0.1);
}

/* Avatar System */
.chat-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.875rem;
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.8);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Action Button System */
.action-button {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: var(--font-size-xs);
  font-weight: 500;
  transition: var(--transition-fast);
  border: none;
  cursor: pointer;
  min-height: 36px; /* Reduced but still accessible */
}

.action-button-primary {
  background: rgba(19, 49, 87, 0.1);
  color: var(--primary-color);
}

.action-button-primary:hover {
  background: rgba(19, 49, 87, 0.15);
  transform: translateY(-1px);
}

.action-button-primary:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* Copy to Clipboard Button */
.copy-button {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: var(--font-size-xs);
  color: #6b7280;
  background: transparent;
  border: 1px solid #e5e7eb;
  cursor: pointer;
  transition: var(--transition-fast);
}

.copy-button:hover {
  background: #f3f4f6;
  color: var(--primary-color);
}

/* Grid System for Chat Cards */
.chat-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--spacing-md);
  padding: var(--spacing-md);
}

@media (min-width: 768px) {
  .chat-grid {
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: var(--spacing-lg);
    padding: var(--spacing-lg);
  }
}

@media (min-width: 1024px) {
  .chat-grid {
    grid-template-columns: repeat(auto-fill, minmax(340px, 1fr));
    gap: var(--spacing-lg);
    padding: var(--spacing-lg);
  }
}

/* Enhanced styles for chat bubbles in light mode with responsive sizing */
.message-bubble-user {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border: 1.5px solid #e0e0e0;
  box-shadow: 0 2px 8px rgba(19, 49, 87, 0.08), 0 1px 4px rgba(19, 49, 87, 0.06);
  border-radius: clamp(16px, 4vw, 20px) clamp(16px, 4vw, 20px) clamp(16px, 4vw, 20px) clamp(4px, 1vw, 6px);
  backdrop-filter: blur(8px);
  transition: all 0.2s ease;
  max-width: clamp(280px, 85%, 600px);
  word-wrap: break-word;
  overflow-wrap: break-word;
  position: relative;
  text-align: left;
  align-self: flex-start;
  padding: clamp(12px, 3vw, 16px) clamp(16px, 4vw, 20px);
  font-size: clamp(0.875rem, 2.5vw, 1rem);
  line-height: clamp(1.4, 3vw, 1.6);
}

.message-bubble-user:hover {
  box-shadow: 0 4px 12px rgba(19, 49, 87, 0.12), 0 2px 6px rgba(19, 49, 87, 0.08);
  transform: translateY(-1px);
}

.message-bubble-agent {
  background: linear-gradient(135deg, #133157 0%, #1a4269 100%);
  border: 1.5px solid rgba(19, 49, 87, 0.2);
  box-shadow: 0 2px 8px rgba(19, 49, 87, 0.15), 0 1px 4px rgba(19, 49, 87, 0.1);
  border-radius: clamp(16px, 4vw, 20px) clamp(16px, 4vw, 20px) clamp(4px, 1vw, 6px) clamp(16px, 4vw, 20px);
  backdrop-filter: blur(8px);
  transition: all 0.2s ease;
  max-width: clamp(280px, 85%, 600px);
  word-wrap: break-word;
  overflow-wrap: break-word;
  position: relative;
  text-align: left;
  align-self: flex-end;
  padding: clamp(12px, 3vw, 16px) clamp(16px, 4vw, 20px);
  font-size: clamp(0.875rem, 2.5vw, 1rem);
  line-height: clamp(1.4, 3vw, 1.6);
}

.message-bubble-agent:hover {
  box-shadow: 0 4px 12px rgba(19, 49, 87, 0.2), 0 2px 6px rgba(19, 49, 87, 0.15);
  transform: translateY(-1px);
}

/* Enhanced styles for chat bubbles in dark mode */
.dark .message-bubble-user {
  background: linear-gradient(135deg, rgba(31, 41, 55, 0.95) 0%, rgba(17, 24, 39, 0.95) 100%);
  border: 1.5px solid rgba(55, 65, 81, 0.6);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3), 0 1px 4px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(12px);
}

.dark .message-bubble-agent {
  background: linear-gradient(135deg, #133157 0%, #0f2643 100%);
  border: 1.5px solid rgba(19, 49, 87, 0.4);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.25), 0 1px 4px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(12px);
}

/* Avatar styles */
.avatar-user {
  background: linear-gradient(135deg, #e0e0e0 0%, #d1d5db 100%);
  color: #133157;
  font-weight: 600;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 8px rgba(19, 49, 87, 0.15);
}

.avatar-agent {
  background: linear-gradient(135deg, #febd49 0%, #f59e0b 100%);
  color: #133157;
  font-weight: 700;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 8px rgba(254, 189, 73, 0.3);
}

.dark .avatar-user {
  border-color: rgba(31, 41, 55, 1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.dark .avatar-agent {
  border-color: rgba(31, 41, 55, 1);
  box-shadow: 0 2px 8px rgba(254, 189, 73, 0.2);
}

/* Enhanced attachment card styles */
.attachment-card {
  padding: 0.5rem 0.75rem;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(4px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.2s ease;
}

.attachment-card:hover {
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.attachment-card .file-icon {
  transition: transform 0.2s ease;
}

.attachment-card:hover .file-icon {
  transform: scale(1.1);
}

/* File type icons styling */
.file-icon {
  color: #133157;
  transition: color 0.2s ease;
}

.attachment-card:hover .file-icon {
  color: #febd49;
}

.dark .file-icon {
  color: #e0e0e0;
}

.dark .attachment-card:hover .file-icon {
  color: #febd49;
}

/* Enhanced websocket indicator styles with application colors */
.ws-connection-indicator {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  border-radius: 8px;
  font-size: 0.75rem;
  font-weight: 500;
}

.ws-connection-indicator-connected {
  color: #10B981;
  background: rgba(16, 185, 129, 0.1);
  border: 1px solid rgba(16, 185, 129, 0.2);
}

.ws-connection-indicator-connecting,
.ws-connection-indicator-reconnecting {
  color: #febd49;
  background: rgba(254, 189, 73, 0.1);
  border: 1px solid rgba(254, 189, 73, 0.2);
  animation: pulse-glow 2s infinite;
}

.ws-connection-indicator-disconnected {
  color: #EF4444;
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
}

@keyframes pulse-glow {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

/* Modern custom scrollbar with application colors */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #133157 0%, #1a4269 100%);
  border-radius: 6px;
  border: 1px solid rgba(224, 224, 224, 0.3);
  transition: all 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #febd49 0%, #f59e0b 100%);
  border-color: rgba(254, 189, 73, 0.5);
  transform: scale(1.1);
}

.dark ::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, rgba(19, 49, 87, 0.8) 0%, rgba(26, 66, 105, 0.8) 100%);
  border-color: rgba(55, 65, 81, 0.3);
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #febd49 0%, #f59e0b 100%);
  border-color: rgba(254, 189, 73, 0.4);
}

/* Enhanced animations with modern easing */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.6;
    transform: scale(0.98);
  }
}

@keyframes typing-dots {
  0%, 20% {
    opacity: 0.4;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
  80%, 100% {
    opacity: 0.4;
    transform: scale(1);
  }
}

@keyframes typing-indicator-slide {
  0% {
    opacity: 0;
    transform: translateY(10px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes typing-indicator-pulse {
  0%, 100% {
    background-color: rgba(19, 49, 87, 0.05);
  }
  50% {
    background-color: rgba(254, 189, 73, 0.1);
  }
}

.animate-pulse-slow {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.typing-dot {
  animation: typing-dots 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) { animation-delay: 0ms; }
.typing-dot:nth-child(2) { animation-delay: 200ms; }
.typing-dot:nth-child(3) { animation-delay: 400ms; }

/* Enhanced typing indicator container */
.typing-indicator-container {
  animation: typing-indicator-slide 0.3s ease-out;
  background: linear-gradient(135deg, rgba(248, 249, 250, 0.9) 0%, rgba(241, 245, 249, 0.9) 100%);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(226, 232, 240, 0.6);
  box-shadow: 0 2px 8px rgba(19, 49, 87, 0.08);
}

.typing-indicator-container::before {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: inherit;
  animation: typing-indicator-pulse 2s infinite ease-in-out;
  z-index: -1;
}

/* Message status icons animation */
.message-status-icon {
  transition: all 0.2s ease;
}

.message-status-sending {
  animation: pulse 1s infinite;
  color: #febd49;
}

.message-status-sent {
  color: #e0e0e0;
}

.message-status-delivered {
  color: #133157;
}

.message-status-read {
  color: #10B981;
}

/* Enhanced search highlighting with application colors */
.search-highlight {
  animation: highlight-pulse 1.5s ease-in-out;
  background: linear-gradient(135deg, rgba(254, 189, 73, 0.2) 0%, rgba(245, 158, 11, 0.2) 100%);
  border-radius: 4px;
  padding: 1px 2px;
}

.search-result {
  position: relative;
  transition: all 0.3s ease;
  animation: pulse 2s infinite;
}

.current-search-result {
  transform: scale(1.02);
  animation: highlight-pulse 2s infinite;
}

.current-search-result::before {
  content: '';
  position: absolute;
  top: -6px;
  bottom: -6px;
  left: -6px;
  right: -6px;
  border: 2px solid #febd49;
  border-radius: 12px;
  pointer-events: none;
  animation: border-pulse 1.5s infinite;
  z-index: -1;
  background: rgba(254, 189, 73, 0.05);
}

@keyframes highlight-pulse {
  0% { 
    background: transparent; 
    transform: scale(1);
  }
  30% { 
    background: linear-gradient(135deg, rgba(254, 189, 73, 0.3) 0%, rgba(245, 158, 11, 0.3) 100%);
    transform: scale(1.05);
  }
  70% { 
    background: linear-gradient(135deg, rgba(254, 189, 73, 0.3) 0%, rgba(245, 158, 11, 0.3) 100%);
    transform: scale(1.05);
  }
  100% { 
    background: transparent; 
    transform: scale(1);
  }
}

@keyframes border-pulse {
  0% { 
    border-color: rgba(254, 189, 73, 0.4);
    box-shadow: 0 0 0 0 rgba(254, 189, 73, 0.3);
  }
  50% { 
    border-color: rgba(254, 189, 73, 0.8);
    box-shadow: 0 0 0 4px rgba(254, 189, 73, 0.1);
  }
  100% { 
    border-color: rgba(254, 189, 73, 0.4);
    box-shadow: 0 0 0 0 rgba(254, 189, 73, 0.3);
  }
}

/* Search highlight effects */
.search-result {
  animation: pulse 2s infinite;
}

.current-search-result {
  animation: highlight-pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

@keyframes highlight-pulse {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(254, 189, 73, 0);
  }
  50% {
    box-shadow: 0 0 0 8px rgba(254, 189, 73, 0.4);
  }
}

/* Message grouping and conversation flow */
.message-group {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-bottom: 20px;
}

.message-group-user {
  align-items: flex-start;
}

.message-group-agent {
  align-items: flex-end;
}

.message-container {
  display: flex;
  max-width: 100%;
  position: relative;
  transition: all 0.3s ease;
  margin-bottom: 16px;
  padding: 0 8px;
}

.message-container-user {
  justify-content: flex-start;
  align-items: flex-start;
}

.message-container-agent {
  justify-content: flex-end;
  align-items: flex-start;
}

/* Enhanced responsive message spacing and alignment with improved scaling */
@media (min-width: 768px) {
  .message-container {
    padding: 0 clamp(16px, 4vw, 24px);
    margin-bottom: clamp(18px, 4vw, 22px);
  }

  .message-bubble-user,
  .message-bubble-agent {
    max-width: clamp(320px, 75%, 650px);
    font-size: clamp(0.9rem, 2.8vw, 1.1rem);
  }
}

@media (min-width: 1024px) {
  .message-container {
    padding: 0 clamp(24px, 5vw, 32px);
    margin-bottom: clamp(20px, 4.5vw, 24px);
  }

  .message-bubble-user,
  .message-bubble-agent {
    max-width: clamp(400px, 70%, 700px);
    font-size: clamp(1rem, 3vw, 1.2rem);
  }
}

@media (min-width: 1440px) {
  .message-bubble-user,
  .message-bubble-agent {
    max-width: clamp(500px, 65%, 800px);
    font-size: clamp(1.1rem, 3.2vw, 1.3rem);
    padding: clamp(16px, 4vw, 24px) clamp(20px, 5vw, 28px);
  }
}

/* Mobile-specific adjustments for better text alignment */
@media (max-width: 767px) {
  .message-bubble-user,
  .message-bubble-agent {
    max-width: clamp(250px, 90%, 350px);
    font-size: clamp(0.85rem, 4vw, 0.95rem);
  }

  .message-container {
    margin-bottom: clamp(14px, 3.5vw, 16px);
    padding: 0 clamp(8px, 2vw, 12px);
  }
}


/* Chat header clearance and positioning */
.chat-messages-container {
  /* Ensure adequate spacing from sticky header */
  padding-top: 8rem; /* 128px base spacing - doubled */
}

@media (min-width: 768px) {
  .chat-messages-container {
    padding-top: 10rem; /* 160px for larger screens - doubled */
  }
}

/* Ensure first message is never hidden behind header */
.message-container:first-child {
  margin-top: 5.5rem;
}

/* Modal-specific spacing adjustments for first message */
/* When ChatDetailView is rendered inside ChatModal, we need different spacing */
.modal-chat-container {
  /* Reduce the overall padding-top for modal context since there's no sticky header above */
  padding-top: 1rem !important; /* Much less padding for modal */
}

.modal-chat-container .message-container:first-child {
  margin-top: 5.5rem; /* Reduced spacing for modal context */
}

@media (min-width: 768px) {
  .modal-chat-container {
    padding-top: 4.5rem !important; /* Slightly more for larger screens */
  }

  .modal-chat-container .message-container:first-child {
    margin-top: 2rem; /* Slightly more spacing on larger screens */
  }
}

@media (min-width: 1024px) {
  .modal-chat-container {
    padding-top: 5rem !important; /* More for desktop modal */
  }

  .modal-chat-container .message-container:first-child {
    margin-top: 5.5rem; /* More spacing for desktop modal */
  }
}

/* Enhanced message bubble positioning */
.message-bubble-user,
.message-bubble-agent {
  /* Ensure proper text wrapping and alignment */
  hyphens: auto;
  word-break: break-word;
  line-height: 1.4;
}

/* Message timestamp styling */
.message-timestamp {
  font-size: 0.7rem;
  color: rgba(19, 49, 87, 0.5);
  margin-top: 4px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.message-container:hover .message-timestamp {
  opacity: 1;
}

.dark .message-timestamp {
  color: rgba(224, 224, 224, 0.6);
}

/* Enhanced message input area with improved cursor handling */
.message-input-container {
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-top: 1px solid #e0e0e0;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 1) 100%);
  backdrop-filter: blur(10px);
  padding: clamp(16px, 4vw, 20px);
}

.dark .message-input-container {
  border-top: 1px solid rgba(55, 65, 81, 0.5);
  background: linear-gradient(to bottom, rgba(31, 41, 55, 0.8) 0%, rgba(17, 24, 39, 1) 100%);
}

/* Enhanced textarea with proper cursor containment */
.message-textarea {
  position: relative;
  width: 100%;
  min-height: clamp(48px, 12vw, 56px);
  max-height: clamp(100px, 25vw, 120px);
  overflow-y: auto;
  overflow-x: hidden;
  padding: clamp(12px, 3vw, 16px) clamp(16px, 4vw, 20px);
  padding-right: clamp(48px, 12vw, 56px);
  background-color: white;
  border: 2px solid #e0e0e0;
  border-radius: clamp(16px, 4vw, 18px);
  resize: none;
  font-size: clamp(0.875rem, 2.5vw, 0.95rem);
  line-height: clamp(1.4, 3vw, 1.6);
  transition: all 0.2s ease;
  box-shadow: 0 2px 6px rgba(19, 49, 87, 0.06);
  /* Prevent cursor overflow */
  text-overflow: ellipsis;
  white-space: pre-wrap;
  word-wrap: break-word;
  box-sizing: border-box;
}

.message-textarea:focus {
  border-color: #133157;
  outline: none;
  box-shadow: 0 0 0 3px rgba(19, 49, 87, 0.15);
  /* Ensure cursor stays within bounds on focus */
  overflow: hidden;
}

.dark .message-textarea {
  background-color: rgba(31, 41, 55, 0.9);
  border-color: rgba(55, 65, 81, 0.6);
  color: white;
}

.dark .message-textarea:focus {
  border-color: #febd49;
  box-shadow: 0 0 0 3px rgba(254, 189, 73, 0.2);
}

/* Character counter */
.character-counter {
  font-size: 0.75rem;
  color: rgba(19, 49, 87, 0.5);
  display: flex;
  justify-content: space-between;
  margin-top: 6px;
  padding: 0 4px;
  transition: color 0.2s ease;
}

.character-counter-warning {
  color: #febd49;
}

.character-counter-danger {
  color: #ef4444;
}

/* Attachment preview area */
.attachment-preview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 0.75rem;
}

.attachment-preview {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  border-radius: 8px;
  background: rgba(19, 49, 87, 0.05);
  border: 1px solid rgba(19, 49, 87, 0.1);
  position: relative;
  transition: all 0.2s ease;
}

.attachment-preview:hover {
  background: rgba(19, 49, 87, 0.08);
}

.attachment-preview-close {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.9);
  color: #f43f5e;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: all 0.2s ease;
  border: 1px solid rgba(244, 63, 94, 0.2);
}

.attachment-preview:hover .attachment-preview-close {
  opacity: 1;
}

.attachment-preview-close:hover {
  background: #f43f5e;
  color: white;
  transform: scale(1.1);
}

/* Scroll to bottom button */
.scroll-to-bottom-button {
  position: absolute;
  right: 1rem;
  bottom: 1rem;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #133157;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(19, 49, 87, 0.2);
  z-index: 5;
  transition: all 0.2s ease;
}

.scroll-to-bottom-button:hover {
  background: #1a4269;
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(19, 49, 87, 0.3);
}

/* Enhanced input area and send button alignment */
.chat-input-wrapper {
  display: flex;
  align-items: flex-end;
  gap: clamp(8px, 2vw, 12px);
  padding: clamp(16px, 4vw, 20px);
}

.chat-input-field {
  flex: 1;
  position: relative;
  overflow: hidden;
  border-radius: clamp(16px, 4vw, 20px);
  transition: all 0.2s ease;
}

.chat-send-button {
  height: clamp(52px, 13vw, 56px);
  min-width: clamp(80px, 20vw, 84px);
  border-radius: clamp(8px, 2vw, 12px);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: clamp(6px, 1.5vw, 8px);
  padding: clamp(12px, 3vw, 16px) clamp(16px, 4vw, 20px);
  font-size: clamp(0.875rem, 2.2vw, 0.95rem);
  transition: all 0.2s ease;
}

/* Responsive adjustments for input area */
@media (min-width: 768px) {
  .chat-input-wrapper {
    padding: clamp(20px, 5vw, 24px);
    gap: clamp(12px, 3vw, 16px);
  }

  .chat-send-button {
    height: clamp(56px, 14vw, 60px);
    min-width: clamp(84px, 21vw, 90px);
  }
}

@media (min-width: 1024px) {
  .chat-input-wrapper {
    padding: clamp(24px, 6vw, 28px);
  }

  .chat-send-button {
    height: clamp(60px, 15vw, 64px);
    min-width: clamp(90px, 22vw, 96px);
  }
}



/* Additional cursor and input field improvements */
.message-textarea::placeholder {
  color: rgba(19, 49, 87, 0.5);
  font-size: inherit;
  line-height: inherit;
}

.dark .message-textarea::placeholder {
  color: rgba(224, 224, 224, 0.6);
}

/* Prevent text selection issues and cursor bleeding */
.message-textarea::-webkit-scrollbar {
  width: 6px;
}

.message-textarea::-webkit-scrollbar-track {
  background: transparent;
}

.message-textarea::-webkit-scrollbar-thumb {
  background: rgba(19, 49, 87, 0.2);
  border-radius: 3px;
}

.message-textarea::-webkit-scrollbar-thumb:hover {
  background: rgba(19, 49, 87, 0.4);
}

/* Ensure proper text wrapping and cursor behavior */
.message-textarea {
  /* Additional properties for better cursor handling */
  caret-color: #133157;
}

.message-textarea::selection {
  background-color: rgba(19, 49, 87, 0.2);
}

.dark .message-textarea {
  caret-color: #febd49;
}

.dark .message-textarea::selection {
  background-color: rgba(254, 189, 73, 0.2);
}

/* File drag and drop styles */
.drag-active {
  border: 2px dashed #133157;
  background-color: rgba(19, 49, 87, 0.05);
}

/* Skeleton Loading Animation */
@keyframes skeleton-loading {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.animate-pulse {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: skeleton-loading 1.5s infinite;
}

/* Focus States for Accessibility */
.chat-card:focus-visible {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

.action-button:focus-visible {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

.copy-button:focus-visible {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .chat-card {
    border-width: 2px;
  }

  .status-badge {
    border: 2px solid currentColor;
  }

  .priority-indicator {
    border: 1px solid currentColor;
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .chat-card,
  .action-button,
  .copy-button,
  .status-badge,
  .priority-indicator {
    transition: none;
  }

  .animate-pulse,
  .animate-spin {
    animation: none;
  }
}

/* Print Styles */
@media print {
  .chat-card {
    break-inside: avoid;
    box-shadow: none;
    border: 1px solid #000;
  }

  .action-button,
  .copy-button {
    display: none;
  }
}

/* Dark Mode Enhancements */
.dark .chat-card {
  background: rgba(31, 41, 55, 0.95);
  border-color: rgba(55, 65, 81, 0.6);
}

.dark .chat-card-header,
.dark .chat-card-footer {
  border-color: rgba(55, 65, 81, 0.4);
}

.dark .chat-card-footer {
  background: linear-gradient(to right, rgba(17, 24, 39, 0.8), rgba(31, 41, 55, 0.8));
}

.dark .action-button-primary {
  background: rgba(254, 189, 73, 0.1);
  color: #febd49;
}

.dark .action-button-primary:hover {
  background: rgba(254, 189, 73, 0.2);
}

.dark .copy-button {
  border-color: rgba(55, 65, 81, 0.6);
  color: rgba(224, 224, 224, 0.8);
}

.dark .copy-button:hover {
  background: rgba(55, 65, 81, 0.3);
  color: #febd49;
}

/* Mobile Optimizations */
@media (max-width: 640px) {
  .chat-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
  }

  .chat-card {
    min-height: 180px;
  }

  .chat-card-header,
  .chat-card-content,
  .chat-card-footer {
    padding: var(--spacing-sm);
  }

  .action-button {
    padding: 6px 10px;
    font-size: 0.75rem;
    min-height: 32px;
  }

  .chat-avatar {
    width: 32px;
    height: 32px;
    font-size: 0.8rem;
  }
}

/* Tablet Optimizations */
@media (min-width: 641px) and (max-width: 1024px) {
  .chat-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  }

  .chat-card {
    min-height: 190px;
  }
}

/* Large Screen Optimizations */
@media (min-width: 1440px) {
  .chat-grid {
    grid-template-columns: repeat(auto-fill, minmax(360px, 1fr));
    gap: var(--spacing-lg);
  }

  .chat-card {
    min-height: 210px;
  }
}