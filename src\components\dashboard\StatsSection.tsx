'use client';

import StatsCard from '@/components/dashboard/StatsCard';
import StatsCardSkeleton from '@/components/dashboard/StatsCardSkeleton';
import { MessageSquare, Clock, CheckCircle2, Users, Timer, TrendingUp, AlertTriangle, BarChart3 } from 'lucide-react';
import { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { getEnhancedAnalytics, EnhancedStatsData } from '@/services/api';

const StatsSection = () => {
  const [stats, setStats] = useState<EnhancedStatsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchStats = async () => {
      setLoading(true);
      setError(null);
      try {
        console.log('Fetching analytics data...');
        const result = await getEnhancedAnalytics();
        console.log('Analytics result:', result);

        if (result.status === 200 && !('error' in result.data)) {
          setStats(result.data as EnhancedStatsData);
          console.log('Analytics data loaded successfully:', result.data);
        } else {
          const errorMessage = 'error' in result.data ? result.data.error : 'Failed to fetch analytics data';
          setError(errorMessage);
          console.error('Error fetching analytics:', errorMessage);
        }
      } catch (error) {
        const errorMessage = 'Failed to fetch analytics data';
        setError(errorMessage);
        console.error('Error fetching stats:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchStats();

    // Set up periodic refresh every 5 minutes
    const interval = setInterval(fetchStats, 5 * 60 * 1000);

    return () => clearInterval(interval);
  }, []);

  if (loading) {
    return (
      <div>
        {/* Section Header */}
        <div className="mb-6">
          <h2 className="text-lg font-semibold text-[#113158] mb-2">
            Panoramica Generale
          </h2>
          <p className="text-sm text-gray-600">
            Caricamento statistiche in tempo reale...
          </p>
        </div>

        {/* Primary Stats Skeleton */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6 mb-6">
          {[...Array(4)].map((_, i) => (
            <StatsCardSkeleton key={`primary-${i}`} />
          ))}
        </div>

        {/* Secondary Stats Skeleton */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6">
          {[...Array(4)].map((_, i) => (
            <StatsCardSkeleton key={`secondary-${i}`} />
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-xl p-6 mb-6">
        <div className="flex items-center gap-3">
          <AlertTriangle className="text-red-600" size={24} />
          <div>
            <h3 className="text-red-800 font-semibold">Errore nel caricamento delle statistiche</h3>
            <p className="text-red-600 text-sm mt-1">{error}</p>
          </div>
        </div>
      </div>
    );
  }

  if (!stats) {
    return null;
  }

  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };
  return (
    <div>
      {/* Section Header */}
      <div className="mb-6">
        <h2 className="text-lg font-semibold text-[#113158] mb-2">
          Panoramica Generale
        </h2>
        <p className="text-sm text-gray-600">
          Statistiche in tempo reale delle conversazioni di supporto
        </p>
      </div>

      {/* Primary Stats Grid */}
      <motion.div
        className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6 mb-6"
        variants={container}
        initial="hidden"
        animate="show"
      >
        <StatsCard
          title="Conversazioni Totali"
          value={stats.total}
          icon={<MessageSquare size={22} />}
          description="Totale richieste"
          trendValue={stats.totalTrend}
          trendDirection={stats.totalTrend > 0 ? 'up' : stats.totalTrend < 0 ? 'down' : 'neutral'}
          color="blue"
          subtitle="Dall'inizio del periodo"
        />
        <StatsCard
          title="In Attesa"
          value={stats.pending}
          icon={<Clock size={22} />}
          description="Richieste in coda"
          trendValue={stats.pendingTrend}
          trendDirection={stats.pendingTrend > 0 ? 'up' : stats.pendingTrend < 0 ? 'down' : 'neutral'}
          color="amber"
          subtitle="Da processare"
        />
        <StatsCard
          title="In Elaborazione"
          value={stats.inProgress}
          icon={<BarChart3 size={22} />}
          description="Attualmente attive"
          color="blue"
          subtitle="In corso di risoluzione"
        />
        <StatsCard
          title="Risolte"
          value={stats.resolved}
          icon={<CheckCircle2 size={22} />}
          description="Completate con successo"
          trendValue={stats.resolvedTrend}
          trendDirection={stats.resolvedTrend > 0 ? 'up' : stats.resolvedTrend < 0 ? 'down' : 'neutral'}
          color="green"
          subtitle="Questa settimana"
        />
      </motion.div>

      {/* Secondary Stats Grid */}
      <motion.div
        className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6"
        variants={container}
        initial="hidden"
        animate="show"
      >
        <StatsCard
          title="Alta Priorità"
          value={stats.highPriority}
          icon={<AlertTriangle size={22} />}
          description="Richieste urgenti"
          color="red"
          subtitle="Richiedono attenzione immediata"
        />
        <StatsCard
          title="Tempo di Risposta"
          value={stats.averageResponseTime}
          unit=" min"
          icon={<Timer size={22} />}
          description="Tempo medio di risposta"
          trendValue={stats.responseTimeTrend}
          trendDirection={stats.responseTimeTrend > 0 ? 'down' : stats.responseTimeTrend < 0 ? 'up' : 'neutral'}
          color="blue"
          subtitle="Media settimanale"
        />
        <StatsCard
          title="Agenti Attivi"
          value={stats.activeAgents}
          icon={<Users size={22} />}
          description="Staff disponibile"
          color="green"
          subtitle="Questa settimana"
        />
        <StatsCard
          title="Tasso di Risoluzione"
          value={stats.resolutionRate}
          unit="%"
          icon={<TrendingUp size={22} />}
          description="Percentuale di successo"
          trendValue={stats.resolutionRateTrend}
          trendDirection={stats.resolutionRateTrend > 0 ? 'up' : stats.resolutionRateTrend < 0 ? 'down' : 'neutral'}
          color="green"
          subtitle="Media del periodo"
        />
      </motion.div>
    </div>
  );
};

export default StatsSection;
