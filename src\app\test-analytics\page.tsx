'use client';

import { useEffect, useState } from 'react';
import { getAnalyticsSummary, getAnalyticsByPriority, getAnalyticsTimeseries, getEnhancedAnalytics } from '@/services/api';

export default function TestAnalyticsPage() {
  const [results, setResults] = useState<any>({});
  const [loading, setLoading] = useState(false);

  const testEndpoints = async () => {
    setLoading(true);
    const testResults: any = {};

    try {
      console.log('Testing analytics summary...');
      const summaryResult = await getAnalyticsSummary();
      testResults.summary = summaryResult;
      console.log('Summary result:', summaryResult);

      console.log('Testing analytics by priority...');
      const priorityResult = await getAnalyticsByPriority();
      testResults.priority = priorityResult;
      console.log('Priority result:', priorityResult);

      console.log('Testing analytics timeseries...');
      const timeseriesResult = await getAnalyticsTimeseries();
      testResults.timeseries = timeseriesResult;
      console.log('Timeseries result:', timeseriesResult);

      console.log('Testing enhanced analytics...');
      const enhancedResult = await getEnhancedAnalytics();
      testResults.enhanced = enhancedResult;
      console.log('Enhanced result:', enhancedResult);

    } catch (error) {
      console.error('Error testing endpoints:', error);
      testResults.error = error;
    }

    setResults(testResults);
    setLoading(false);
  };

  useEffect(() => {
    testEndpoints();
  }, []);

  return (
    <div className="container mx-auto p-6">
      <h1 className="text-2xl font-bold mb-6">Analytics API Test</h1>
      
      <button 
        onClick={testEndpoints}
        disabled={loading}
        className="bg-blue-500 text-white px-4 py-2 rounded mb-6 disabled:opacity-50"
      >
        {loading ? 'Testing...' : 'Test Endpoints'}
      </button>

      <div className="space-y-6">
        {Object.entries(results).map(([key, value]) => (
          <div key={key} className="border p-4 rounded">
            <h2 className="text-lg font-semibold mb-2">{key}</h2>
            <pre className="bg-gray-100 p-2 rounded text-sm overflow-auto">
              {JSON.stringify(value, null, 2)}
            </pre>
          </div>
        ))}
      </div>
    </div>
  );
}
