'use client';

import { ReactNode } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { motion } from 'framer-motion';
import { TrendingUp, TrendingDown, Minus } from 'lucide-react';

interface StatsCardProps {
  title: string;
  value: string | number;
  description?: string;
  icon: ReactNode;
  trendValue?: number;
  trendDirection?: 'up' | 'down' | 'neutral';
  color?: 'blue' | 'yellow' | 'green' | 'red' | 'amber';
  unit?: string;
  subtitle?: string;
}

// Utility function to format numbers with proper localization
const formatNumber = (value: number | string): string => {
  const num = typeof value === 'string' ? parseFloat(value) : value;
  if (isNaN(num)) return '0';

  // Format with Italian locale for proper number formatting
  return new Intl.NumberFormat('it-IT').format(num);
};

// Utility function to format percentage
const formatPercentage = (value: number): string => {
  return `${value > 0 ? '+' : ''}${value}%`;
};

const StatsCard = ({
  title,
  value,
  description,
  icon,
  trendValue,
  trendDirection = 'neutral',
  color = 'blue',
  unit,
  subtitle,
}: StatsCardProps) => {
  // Enhanced color classes with improved accessibility and user preferences
  const colorClasses = {
    blue: {
      bg: 'bg-gradient-to-br from-blue-50 to-blue-100/80',
      iconBg: 'bg-gradient-to-br from-blue-600 to-blue-700',
      text: 'text-blue-900',
      border: 'border-blue-200',
      hover: 'hover:shadow-xl hover:shadow-blue-500/20 hover:border-blue-300',
      iconShadow: 'shadow-lg shadow-blue-600/30',
      trendUp: 'text-green-700 bg-green-100',
      trendDown: 'text-red-700 bg-red-100',
      trendNeutral: 'text-gray-600 bg-gray-100',
      accent: 'bg-blue-100/60'
    },
    amber: {
      bg: 'bg-gradient-to-br from-amber-50 to-amber-100/80',
      iconBg: 'bg-gradient-to-br from-amber-500 to-amber-600',
      text: 'text-amber-900',
      border: 'border-amber-200',
      hover: 'hover:shadow-xl hover:shadow-amber-500/20 hover:border-amber-300',
      iconShadow: 'shadow-lg shadow-amber-500/30',
      trendUp: 'text-red-700 bg-red-100', // Amber represents waiting, so increase is bad
      trendDown: 'text-green-700 bg-green-100', // Decrease in waiting is good
      trendNeutral: 'text-gray-600 bg-gray-100',
      accent: 'bg-amber-100/60'
    },
    yellow: {
      bg: 'bg-gradient-to-br from-yellow-50 to-yellow-100/80',
      iconBg: 'bg-gradient-to-br from-yellow-500 to-yellow-600',
      text: 'text-yellow-900',
      border: 'border-yellow-200',
      hover: 'hover:shadow-xl hover:shadow-yellow-500/20 hover:border-yellow-300',
      iconShadow: 'shadow-lg shadow-yellow-500/30',
      trendUp: 'text-red-700 bg-red-100',
      trendDown: 'text-green-700 bg-green-100',
      trendNeutral: 'text-gray-600 bg-gray-100',
      accent: 'bg-yellow-100/60'
    },
    green: {
      bg: 'bg-gradient-to-br from-green-50 to-green-100/80',
      iconBg: 'bg-gradient-to-br from-green-600 to-green-700',
      text: 'text-green-900',
      border: 'border-green-200',
      hover: 'hover:shadow-xl hover:shadow-green-500/20 hover:border-green-300',
      iconShadow: 'shadow-lg shadow-green-600/30',
      trendUp: 'text-green-700 bg-green-100',
      trendDown: 'text-red-700 bg-red-100',
      trendNeutral: 'text-gray-600 bg-gray-100',
      accent: 'bg-green-100/60'
    },
    red: {
      bg: 'bg-gradient-to-br from-red-50 to-red-100/80',
      iconBg: 'bg-gradient-to-br from-red-600 to-red-700',
      text: 'text-red-900',
      border: 'border-red-200',
      hover: 'hover:shadow-xl hover:shadow-red-500/20 hover:border-red-300',
      iconShadow: 'shadow-lg shadow-red-600/30',
      trendUp: 'text-red-700 bg-red-100',
      trendDown: 'text-green-700 bg-green-100',
      trendNeutral: 'text-gray-600 bg-gray-100',
      accent: 'bg-red-100/60'
    },
  };
  // Enhanced trend logic with proper icons and colors
  const getTrendIcon = () => {
    switch (trendDirection) {
      case 'up':
        return <TrendingUp size={14} className="text-current" />;
      case 'down':
        return <TrendingDown size={14} className="text-current" />;
      default:
        return <Minus size={14} className="text-current" />;
    }
  };

  const trendColorClass = trendDirection === 'up'
    ? colorClasses[color].trendUp
    : trendDirection === 'down'
    ? colorClasses[color].trendDown
    : colorClasses[color].trendNeutral;

  // Format the main value with proper number formatting
  const formattedValue = formatNumber(value);
  const displayValue = unit ? `${formattedValue}${unit}` : formattedValue;  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      whileHover={{ 
        y: -4, 
        boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1)",
        transition: { duration: 0.2 } 
      }}
      className="min-h-[130px]"
    >
      <Card
        className={`border ${colorClasses[color].border} shadow-sm hover:shadow-xl overflow-hidden rounded-xl ${colorClasses[color].bg} ${colorClasses[color].hover} transition-all duration-300 cursor-pointer h-full backdrop-blur-sm`}
        role="article"
        aria-label={`${title}: ${displayValue}`}
      >
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4 pt-6 px-6">
          <div className="flex flex-col">
            <CardTitle className={`text-sm font-semibold ${colorClasses[color].text} tracking-wide mb-1`}>
              {title}
            </CardTitle>
            {subtitle && (
              <p className="text-xs text-gray-500 font-normal">
                {subtitle}
              </p>
            )}
          </div>
          <motion.div
            className={`rounded-xl p-3 ${colorClasses[color].iconBg} text-white ${colorClasses[color].iconShadow}`}
            whileHover={{ scale: 1.05, rotate: 5 }}
            whileTap={{ scale: 0.95 }}
            transition={{ type: "spring", stiffness: 400, damping: 10 }}
            aria-hidden="true"
          >
            {icon}
          </motion.div>
        </CardHeader>
        <CardContent className="px-6 pb-6">
          <motion.div
            className={`text-4xl font-bold ${colorClasses[color].text} mb-3 tracking-tight`}
            initial={{ scale: 1 }}
            whileHover={{ scale: 1.02 }}
            transition={{ type: "spring", stiffness: 400, damping: 10 }}
          >
            {displayValue}
          </motion.div>

          {/* Enhanced description and trend section */}
          <div className="space-y-3">
            {description && (
              <p className="text-sm text-gray-600 font-medium leading-relaxed">
                {description}
              </p>
            )}

            {trendValue !== undefined && (
              <div className="flex items-center gap-2 flex-wrap">
                <span
                  className={`flex items-center gap-1.5 ${trendColorClass} py-2 px-3 rounded-full text-xs font-semibold transition-all duration-200`}
                  role="status"
                  aria-label={`Trend: ${formatPercentage(trendValue)} ${trendDirection === 'up' ? 'increase' : trendDirection === 'down' ? 'decrease' : 'no change'}`}
                >
                  {getTrendIcon()}
                  {formatPercentage(Math.abs(trendValue))}
                </span>
                <span className="text-gray-500 text-xs font-medium">
                  {trendDirection === 'up' ? 'in più' : trendDirection === 'down' ? 'in meno' : 'stabile'} questa settimana
                </span>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default StatsCard;
